import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/shared/widgets/row/text_row.dart';

class WithdrawProgressCell extends StatefulWidget {
  final WithdrawRecord model;
  final bool isExpanded;
  final Future<bool> Function() onTapCell;

  const WithdrawProgressCell({
    super.key,
    required this.model,
    this.isExpanded = false,
    required this.onTapCell,
  });

  @override
  State<StatefulWidget> createState() => _WithdrawProgressCellState();
}

class _WithdrawProgressCellState extends State<WithdrawProgressCell> {
  late WithdrawRecord model;
  late bool isRead; // 是否已读

  bool _isExpanded = false;

  @override
  void initState() {
    model = widget.model;
    isRead = widget.model.read;
    _isExpanded = widget.isExpanded;
    if (_isExpanded) {
      operateReadStatus(updateFloatBtnTitle: true);
    }
    super.initState();
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  void didUpdateWidget(WithdrawProgressCell oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.model.orderStatus != oldWidget.model.orderStatus || widget.model.read != oldWidget.model.read) {
      setState(() {
        model = widget.model;
      });
    }
  }

  operateReadStatus({bool updateFloatBtnTitle = false}) async {
    if (!isRead) {
      // 未读时请求接口
      final flag = await widget.onTapCell.call();
      if (flag != isRead) {
        setState(() {
          model.read = isRead = true;
        });
      }
      if (updateFloatBtnTitle) sl<UserCubit>().onChangeWithdrawFloatBtnTitle(model);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        _toggleExpand();
        operateReadStatus();
      },
      child: Container(
        clipBehavior: Clip.hardEdge,
        margin: EdgeInsets.symmetric(horizontal: 10.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.highlightForeground,
          borderRadius: BorderRadius.circular(12.gw),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            if (_isExpanded) ...[
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildDetailList(),
                    SizedBox(height: 8.gw),
                    _buildProgressBar(),
                    SizedBox(height: 18.gw),
                  ],
                ),
              )
            ]
          ],
        ),
      ),
    );
  }

  _buildHeader() {
    return Container(
      height: 48.gw,
      color: context.colorTheme.borderA,
      padding: EdgeInsets.symmetric(horizontal: 14.5.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildStatusIcon(),
          SizedBox(width: 4.gw),
          _buildOrderStatusText(),
          SizedBox(width: 4.gw),
          _buildUnreadStatusPot(),
          const Spacer(),
          Text(
            TimeUtil.formatTransactionTime(model.requestTime),
            style: TextStyle(color: const Color(0xff9DA3AE), fontSize: 12.fs),
          ),
          SizedBox(width: 8.gw),
          SizedBox(
            width: 20.gw,
            child: ExpandIcon(
              padding: EdgeInsets.zero,
              isExpanded: _isExpanded,
              color: const Color(0xff9DA3AE),
              size: 20.gw,
              onPressed: (isExpanded) {},
            ),
          ),
        ],
      ),
    );
  }

  _buildStatusIcon() {
    String iconPath = "assets/images/transact/icon_transact_pending.svg";
    switch (model.orderStatus) {
      // 0待审核
      case 0:
        iconPath = "assets/images/transact/icon_transact_pending.svg";
        break;

      // 1已通过
      case 1:
        iconPath = "assets/images/transact/icon_transact_success.svg";
        break;

      // 2已取消 3已拒绝
      case 2:
      case 3:
        iconPath = "assets/images/transact/icon_transact_failed.svg";
        break;
    }
    return SvgPicture.asset(
      iconPath,
      width: 20.gw,
      height: 20.gw,
    );
  }

  _buildOrderStatusText() {
    var title = "";
    var titleColor = Colors.white;
    switch (model.orderStatus) {
      case 0:
        title = "withdraw_progress_pending_review".tr(); // 等待审核
        titleColor = context.colorTheme.textSecondary;
        break;
      case 1:
        title = "withdraw_progress_approved".tr(); // 审核通过
        titleColor = const Color(0xff22C55E);
        break;
      case 2:
        title = "withdraw_progress_order_cancelled".tr(); // 订单取消
        titleColor = const Color(0xffFF4D4F);
        break;
      case 3:
        title = "withdraw_progress_rejected".tr(); // 审核驳回
        titleColor = const Color(0xffFF4D4F);
        break;
      case 5:
        title = "withdraw_status_failed".tr(); // 出款失败
        titleColor = const Color(0xffFF4D4F);
        break;
    }
    return Text(
      title,
      style: TextStyle(
        fontSize: 14.fs,
        color: titleColor,
      ),
    );
  }

  _buildUnreadStatusPot() {
    if (isRead) return const SizedBox.shrink();
    return Container(
      width: 5,
      height: 5,
      decoration: const BoxDecoration(
          color: Colors.redAccent,
          borderRadius: BorderRadius.all(
            Radius.circular(2.5),
          )),
    );
  }

  _buildDetailList() {
    return Container(
      // color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 14.5.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextRow(
            title: "withdrawal_amount".tr(), // 提现金额
            content: model.orderAmount.formattedMoney,
          ),
          if (model.orderInitialAmount != model.finalAmount && model.orderStatus == 1)
            _buildTextRow(
              title: "withdraw_credited_amount".tr(), // 到账金额
              content: model.finalAmount.formattedMoney,
            ),
          _buildTextRow(
            title: "withdraw_methods".tr(), // 提现方式
            content: "${model.cashoutWayName}-${model.cashoutTypeName}",
          ),
          if (model.cardNo.isNotEmpty)
            _buildTextRow(
              title: "receiving_account".tr(), // 收款账号
              content: model.cardNo,
            ),
          _buildTextRow(
            title: "withdraw_order_no".tr(), // 订单编号
            showSeparator: false,
            rightWidget: GSTextImageButton(
              text: model.transactionNo,
              textStyle: context.textTheme.secondary,
              imageAssets: "assets/images/common/icon_copy_black.png",
              position: GSTextImageButtonPosition.right,
              onPressed: () => ClipboardTool.setData(model.transactionNo),
            ),
          ),
        ],
      ),
    );
  }

  _buildTextRow({String? title, String? content, Widget? rightWidget, bool showSeparator = true}) {
    return TextRow(
      padding: EdgeInsets.symmetric(vertical: 12.gw),
      title: title,
      titleStyle: context.textTheme.secondary,
      content: content,
      contentStyle: context.textTheme.secondary,
      contentGap: 60.gw,
      showSeparator: showSeparator,
      rightWidget: rightWidget,
    );
  }

  _buildProgressBar() {
    List<String> titleList = [
      "withdraw_progress_submitted".tr(),
      "withdraw_progress_pending_review".tr(),
      "withdraw_progress_completed".tr()
    ];
    // ["提交申请", "等待审核", "订单完成"];
    List<String> iconPathList = [
      "assets/images/transact/icon_transact_success_gold.svg",
      "assets/images/transact/icon_transact_success_gold.svg",
      "assets/images/transact/icon_transact_success_gold.svg",
    ];
    List<Color> textColorList = [
      const Color(0xffB39572),
      const Color(0xffB39572),
      const Color(0xffB39572),
    ];
    List<double> opacityList = [1, 1, 0.5];

    switch (model.orderStatus) {
      // 待审核
      case 0:
        break;
      // 已通过
      case 1:
        opacityList = [1, 1, 1];
        break;

      // 已取消
      case 2:
        opacityList = [1, 1, 1];
        titleList.last = "withdraw_progress_order_cancelled".tr(); // 订单取消
        textColorList.last = const Color(0xffFF4D4F);
        iconPathList.last = "assets/images/transact/icon_transact_failed.svg";
      // 已拒绝
      case 3:
        opacityList = [1, 1, 1];
        iconPathList.last = "assets/images/transact/icon_transact_failed.svg";
        textColorList.last = const Color(0xffFF4D4F);
        titleList.last = "withdraw_progress_rejected".tr(); // 审核驳回
        break;
      case 5:
        opacityList = [1, 1, 1];
        iconPathList.last = "assets/images/transact/icon_transact_failed.svg";
        textColorList.last = const Color(0xffFF4D4F);
        titleList.last = "withdraw_status_failed".tr(); // 出款失败
        break;
    }
    return Container(
      height: 56.gw,
      alignment: Alignment.center,
      child: ListView.separated(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) {
            return Opacity(
              opacity: opacityList[index],
              child: Column(
                children: [
                  SvgPicture.asset(
                    iconPathList[index],
                    width: 24.gw,
                    height: 24.gw,
                  ),
                  Text(
                    titleList[index],
                    style: TextStyle(color: textColorList[index], fontSize: 14.fs),
                  ),
                ],
              ),
            );
          },
          separatorBuilder: (c, i) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 23.gw),
              child: SvgPicture.asset(
                "assets/images/transact/icon_status_progress_separator.svg",
                width: 16.gw,
                height: 16.gw,
              ),
            );
          },
          itemCount: titleList.length),
    );
  }
}
