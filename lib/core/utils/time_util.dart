import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/constants/enums.dart';

class TimeUtil {
  /// 比赛开始时间 将时间戳转换为 "年-月-日 时：分"
  static String convertTimeStampToFull(int timeStamp) {
    var date = DateTime.fromMillisecondsSinceEpoch(timeStamp);
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  /// 计算两个时间字符串之间的时间差（以秒为单位）：
  static int calculateTimeDifferenceInSeconds(String startTime, String endTime) {
    try {
      DateFormat format = DateFormat("yyyy-MM-dd HH:mm:ss");

      DateTime startDate = format.parse(startTime);
      DateTime endDate = format.parse(endTime);

      Duration difference = endDate.difference(startDate);

      return difference.inSeconds;
    } catch (e) {
      return -1;
    }
  }

  /// Get today's start and end times
  static (String, String) getTodayRange() {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day - 1, 0, 0, 0);
    final endOfDay = DateTime(now.year, now.month, now.day - 1, 23, 59, 59);

    final DateFormat format = DateFormat('yyyy-MM-dd HH:mm:ss');
    return (
      format.format(startOfDay), // Start of the day (00:00:00)
      format.format(endOfDay), // End of the day (23:59:59)
    );
  }

  static String convertTimeStampToTime(int timeStamp) {
    var date = DateTime.fromMillisecondsSinceEpoch(timeStamp);
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(date);
  }

  /// 将秒数转换为智能倒计时格式 (自动显示天时分秒)
  static String toCountdownAuto(int totalSeconds) {
    if (totalSeconds <= 0) {
      return '00:00';
    }

    int days = totalSeconds ~/ 86400; // 一天有86400秒
    int hours = (totalSeconds % 86400) ~/ 3600;
    int remainingSeconds = totalSeconds % 3600;
    int minutes = remainingSeconds ~/ 60;
    int seconds = remainingSeconds % 60;

    // 根据时长选择不同的显示格式
    if (days > 0) {
      // 有天数时显示 x天xx:xx:xx
      return '$days天${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else if (hours > 0) {
      // 有小时时显示 xx:xx:xx
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      // 只有分钟和秒时显示 xx:xx
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 将秒数转换为完整时分秒格式 (始终显示时分秒)
  static String toCountdownFull(int totalSeconds) {
    if (totalSeconds <= 0) {
      return '00:00:00';
    }

    int hours = totalSeconds ~/ 3600;
    int remainingSeconds = totalSeconds % 3600;
    int minutes = remainingSeconds ~/ 60;
    int seconds = remainingSeconds % 60;

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 将秒数转换为分秒格式 (只显示分秒)
  static String toCountdownMinSec(int totalSeconds) {
    if (totalSeconds <= 0) {
      return '00:00';
    }

    int minutes = totalSeconds ~/ 60;
    int seconds = totalSeconds % 60;

    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Extracts date only (yyyy-MM-dd) from a date string
  static String dateOnlyFromString(String? dateTimeStr) {
    if (dateTimeStr == null || dateTimeStr.isEmpty) return '';
    try {
      final dt = DateTime.parse(dateTimeStr);
      return DateFormat('yyyy-MM-dd').format(dt.toLocal());
    } catch (e) {
      return dateTimeStr;
    }
  }

  /// Extracts time only (HH:mm:ss) from a date string
  static String timeOnlyFromString(String? dateTimeStr) {
    if (dateTimeStr == null || dateTimeStr.isEmpty) return '';
    try {
      final dt = DateTime.parse(dateTimeStr);
      return DateFormat('HH:mm:ss').format(dt.toLocal());
    } catch (e) {
      return '';
    }
  }

  /// Formats a datetime string or timestamp for transaction display (MM-dd HH:mm)
  static String formatTransactionTime(dynamic dateTime) {
    if (dateTime == null) return '';

    try {
      DateTime dt;

      if (dateTime is int) {
        // Handle timestamp (milliseconds since epoch)
        dt = DateTime.fromMillisecondsSinceEpoch(dateTime);
      } else if (dateTime is String) {
        if (dateTime.isEmpty) return '';
        // Try to parse as timestamp first
        final timestamp = int.tryParse(dateTime);
        if (timestamp != null) {
          dt = DateTime.fromMillisecondsSinceEpoch(timestamp);
        } else {
          // Parse as date string
          dt = DateTime.parse(dateTime);
        }
      } else {
        return dateTime.toString();
      }

      return DateFormat('MM-dd HH:mm').format(dt.toLocal());
    } catch (e) {
      // If parsing fails, try to return the original value as string
      return dateTime.toString();
    }
  }

  static (int startDate, int endDate) getDateRange(RecordDateType type, {String? dateFormatStr}) {
    final now = DateTime.now().toUtc().add(const Duration(hours: 8)); // 转换为北京时间
    final startOfToday = DateTime(now.year, now.month, now.day, 00, 00, 00);
    final endOfToday = DateTime(now.year, now.month, now.day, 23, 59, 59);
    final startOfYesterday = startOfToday.subtract(const Duration(days: 1));
    final endOfYesterday = startOfToday.subtract(const Duration(seconds: 1));
    final startOfWeek = startOfToday.subtract(const Duration(days: 6)); // 计算近七日的开始日期

    switch (type) {
      case RecordDateType.today:
        return (
          startOfToday.millisecondsSinceEpoch,
          endOfToday.millisecondsSinceEpoch,
        );
      case RecordDateType.yesterday:
        return (
          startOfYesterday.millisecondsSinceEpoch,
          endOfYesterday.millisecondsSinceEpoch,
        );
      case RecordDateType.week:
        return (
          startOfWeek.millisecondsSinceEpoch,
          endOfToday.millisecondsSinceEpoch,
        );
      default:
        throw ArgumentError('Invalid RecordDateType');
    }
  }
}
