import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/common_app_bar.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/shared/widgets/row/text_row.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/transact/top_up/top_up_history_cell.dart';

class TopUpHistoryDetailPage extends StatelessWidget {
  final TopUpRecord model;

  const TopUpHistoryDetailPage({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(pageTitle: "recharge_detail".tr()), // 充值详情
      body: CommonCardPage(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
            margin: EdgeInsets.fromLTRB(15.gw, 10.gw, 15.gw, 0),
            decoration:
                BoxDecoration(color: context.theme.cardColor, borderRadius: BorderRadius.circular(4.gw), boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 0),
              ),
            ]),
            child: Column(
              children: [
                TextRow(
                  title: "current_status".tr(),
                  rightWidget: TopUpHistoryCell.buildOrderStatusWidget(context, orderStatus: model.orderStatus),
                ),
                TextRow(
                  title: "deposit_order_no".tr(),
                  rightWidget: GSTextImageButton(
                    text: model.transactionNo,
                    textStyle: Theme.of(context).textTheme.titleMedium,
                    imageAssets: "assets/images/common/icon_copy_black.png",
                    position: GSTextImageButtonPosition.right,
                    onPressed: () {
                      ClipboardTool.setData(model.transactionNo);
                    },
                  ),
                ),
                TextRow(
                  title: "act_event_type".tr(),
                  content: "${model.cashinWayName}-${model.cashinTypeName}",
                ),
                TextRow(
                  title: "deposit_channel".tr(),
                  content: model.cashinChannel,
                ),
                if (model.depositor.isNotEmpty)
                  TextRow(
                    title: "depositor_name".tr(),
                    content: model.depositor,
                  ),
                // if (model.orderInitialAmount > 0)
                //   TextRow(
                //     title: "订单最初金额(未换算汇率前)",
                //     content: "${model.orderInitialAmount}",
                //   ),
                if (model.orderAmount > 0)
                  TextRow(
                    title: "order_amount".tr(),
                    content: "${model.orderAmount}",
                  ),
                if (model.realAmount > 0)
                  TextRow(
                    title: "actual_deposit_amount".tr(),
                    content: "${model.realAmount}",
                  ),
                if (model.reduceAmount > 0)
                  TextRow(
                    title: "discount_amount".tr(),
                    content: "${model.reduceAmount}",
                  ),
                TextRow(
                  title: "service_charge_rate".tr(),
                  content: "${model.serviceChargeRate}%",
                ),
                TextRow(
                  title: "service_charge".tr(),
                  content: "${model.serviceCharge}",
                ),
                if (model.exchangeRate > 0)
                  TextRow(
                    title: "exchange_rate".tr(),
                    content: "${model.exchangeRate}",
                  ),
                if (model.finalAmount > 0)
                  TextRow(
                    title: "deposit_credited_amount".tr(),
                    content: "${model.finalAmount}",
                  ),
                if (model.cardNo.isNotEmpty)
                  TextRow(
                    title: "transfer_bank_card_number".tr(),
                    content: model.cardNo,
                  ),
                if (model.cardRealName.isNotEmpty)
                  TextRow(
                    title: "receiving_bank_card_account_name".tr(),
                    content: model.cardRealName,
                  ),
                TextRow(
                  title: "recharge_time".tr(),
                  content: TimeUtil.formatTransactionTime(model.requestTime),
                ),
                TextRow(
                  title: "operation_time".tr(),
                  content: TimeUtil.formatTransactionTime(model.operateTime),
                ),
                if (model.integral > 0)
                  TextRow(
                    title: "added_points".tr(),
                    content: "${model.integral}",
                  ),
                TextRow(
                  title: "is_today_first_deposit".tr(),
                  content: model.isTodayFirst == 1 ? "yes".tr() : "no".tr(),
                ),
                if (model.serialNumber.isNotEmpty)
                  TextRow(
                    title: "external_transaction_id".tr(),
                    content: model.serialNumber,
                  ),
                if (model.currency.isNotEmpty)
                  TextRow(
                    title: "currency".tr(),
                    content: model.currency,
                  ),
                TextRow(
                  title: "remarks".tr(),
                  content: model.remark,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
